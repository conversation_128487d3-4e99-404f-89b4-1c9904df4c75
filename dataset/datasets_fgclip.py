import os
import json
from typing import Dict, List
from torch.utils.data import Dataset
import numpy as np
import pandas as pd
import glob
from PIL import Image
import random


def lower_resolution(img: Image):
    """Lower image resolution if too large"""
    h, w = img.size
    if h > 1000 and w > 1000:
        return img.resize((h//10, w//10))
    else:
        return img


def normalize_bbox(bbox, image_width, image_height):
    """Normalize bbox coordinates to [0, 1] range"""
    x1, y1, x2, y2 = bbox[:4]
    return [x1/image_width, y1/image_height, x2/image_width, y2/image_height]


class FGCLIPDataset(Dataset):
    """FGClip Dataset following DAM structure."""

    def __init__(
        self, 
        data_path: str, 
        mode: str = 'pretrained',
        max_length: int = 100000,
    ) -> None:
        super(FGCLIPDataset, self).__init__()
        
        # Load parquet files from directory
        self.data_path = data_path
        self.parquet_files = glob.glob(os.path.join(data_path, '*.parquet'))
        
        if not self.parquet_files:
            raise ValueError(f"No parquet files found in {data_path}")
        
        # Load all data and calculate lengths
        self.datasets = []
        self.lengths = []
        
        for parquet_file in self.parquet_files:
            df = pd.read_parquet(parquet_file)
            self.datasets.append(df)
            self.lengths.append(len(df))
        
        self.max_length = max_length
        self.mode = mode

    def __len__(self) -> int:
        return self.max_length
    
    def get_dataset_idx(self, index):
        """Get dataset index and item index, similar to DAM"""
        for i, ds_len in enumerate(self.lengths):
            if index < ds_len:
                return i, index
            else:
                index -= ds_len
        # If index exceeds total length, wrap around
        total_length = sum(self.lengths)
        index = index % total_length
        for i, ds_len in enumerate(self.lengths):
            if index < ds_len:
                return i, index
            else:
                index -= ds_len

    def construct_messages(self, idx: int):
        """Construct messages following DAM format"""
        # Get dataset and item index
        dataset_idx, item_idx = self.get_dataset_idx(idx)
        df = self.datasets[dataset_idx]
        item = df.iloc[item_idx]
        
        # Extract data from parquet row
        caption = item.get("caption", "")
        short_caption = item.get("short_caption", "")
        f_path = item.get("f_path", "")
        bbox_info = item.get("bbox_info", [])
        
        # Load image
        try:
            # Handle different image storage formats
            if 'image' in item and item['image'] is not None:
                # Image stored as bytes in parquet
                from io import BytesIO
                image = Image.open(BytesIO(item['image'])).convert("RGB")
            elif f_path:
                # Image stored as file path
                # Clean up path if needed
                if "grit-20m/data-12m/" in f_path:
                    f_path = f_path.replace("grit-20m/data-12m/", "")
                
                image_path = os.path.join(self.data_path, f_path)
                if not os.path.exists(image_path):
                    # Try alternative path structures
                    image_path = f_path
                
                image = Image.open(image_path).convert("RGB")
            else:
                # Create dummy image if no image found
                image = Image.new('RGB', (224, 224), color='white')
                
        except Exception as e:
            print(f"Warning: Could not load image for item {idx}: {e}")
            # Create dummy image
            image = Image.new('RGB', (224, 224), color='white')
        
        # Lower resolution if needed
        image = lower_resolution(image)
        
        # Process bbox info
        if bbox_info and len(bbox_info) > 0:
            # Select a random bbox annotation
            selected_bbox = random.choice(bbox_info)
            bbox = selected_bbox.get("bbox", [0, 0, 1, 1])
            
            # Normalize bbox coordinates
            image_width, image_height = image.size
            normalized_bbox = normalize_bbox(bbox, image_width, image_height)
            
            # Get bbox description
            bbox_text = selected_bbox.get("short_expr", "") or selected_bbox.get("long_expr", "")
            if not bbox_text:
                bbox_text = short_caption or caption
            
            # Construct message with bbox (following DAM format)
            message = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": image, "box": normalized_bbox},
                        {"type": "text", "text": f"\nDescribe the region in the image bounded by a red box."}
                    ]
                },
                {
                    "role": "assistant", 
                    "content": [
                        {"type": "text", "text": bbox_text}
                    ]
                }
            ]
        else:
            # No bbox info, use full image with caption (fallback)
            text = caption or short_caption or "This is an image."
            message = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": image},
                        {"type": "text", "text": f"\nDescribe this image."}
                    ]
                },
                {
                    "role": "assistant",
                    "content": [
                        {"type": "text", "text": text}
                    ]
                }
            ]
        
        return message

    def get_instance(self, index):
        """Get instance following DAM format"""
        message = self.construct_messages(index)
        return message 

    def __getitem__(self, i) -> Dict[str, List]: 
        """Get item following DAM format - returns two instances"""
        j = i + self.max_length
        return self.get_instance(i), self.get_instance(j)


# FGClip Collator (can be used with existing qwen2_5_vl collator)
# The FGCLIPDataset returns the same message format as DAM, so it can use
# the existing Qwen2_5VLDataCollator directly.

# However, if you need a custom collator, here's an example:
from typing import Dict, Sequence
import torch

class FGCLIPDataCollator:
    """
    Custom collator for FGClip dataset.
    Since FGClip returns the same format as DAM, this is mostly for reference.
    In practice, you can use the existing Qwen2_5VLDataCollator.
    """

    def __init__(self, processor, tokenizer):
        self.processor = processor
        self.tokenizer = tokenizer
        self.IGNORE_TOKEN_ID = -100

    @property
    def PAD_TOKEN_ID(self) -> int:
        return self.tokenizer.pad_token_id

    def __call__(self, messages: Sequence[Dict]) -> Dict[str, torch.Tensor]:
        # Extract messages from the batch
        # Each item in messages is a tuple of (message1, message2) from __getitem__
        new_messages = []
        for item in messages:
            # item is (message1, message2), we take both
            new_messages.extend(item)

        # Process vision info (images with boxes)
        from collators.qwen2_5_vision_process import process_vision_info_with_focal

        image_nofocal, image_focal_full, image_focal_crop, resort_id = process_vision_info_with_focal(
            new_messages, box_op="crop"
        )

        # Process text
        texts = [
            self.processor.apply_chat_template(msg, tokenize=False, add_generation_prompt=False)
            for msg in new_messages
        ]
        texts = [texts[i] for i in resort_id]

        # Combine images
        image_inputs = image_nofocal + image_focal_crop

        # Process with processor
        inputs = self.processor(
            text=texts,
            images=image_inputs,
            videos=None,
            padding=True,
            return_tensors="pt",
        )

        # Prepare labels
        input_ids = inputs['input_ids']
        labels = input_ids.clone()
        labels[labels == self.PAD_TOKEN_ID] = self.IGNORE_TOKEN_ID

        # Extract components
        attention_mask = inputs.get('attention_mask', None)
        pixel_values = inputs.get('pixel_values', None)
        image_grid_thw = inputs.get('image_grid_thw', None)

        # Handle focal images
        if image_focal_full:
            prefix_img_nums = len(image_nofocal)
            prefix_token_length = image_grid_thw[:prefix_img_nums].prod(1).sum().item()

            focal_crop_pixel_values = pixel_values[prefix_token_length:]
            focal_crop_grid_thw = image_grid_thw[prefix_token_length:]

            nofocal_full_pixel_values = pixel_values[:prefix_token_length]
            nofocal_full_grid_thw = image_grid_thw[:prefix_token_length]

            focal_inputs = self.processor.image_processor(
                images=image_focal_full,
                return_tensors="pt",
            )
            focal_full_pixel_values = focal_inputs.pixel_values
            focal_full_grid_thw = focal_inputs.image_grid_thw

            focal_image_ids = torch.arange(prefix_img_nums, prefix_img_nums + len(image_focal_full))

            return dict(
                input_ids=input_ids,
                attention_mask=attention_mask,
                pixel_values=torch.cat([nofocal_full_pixel_values, focal_full_pixel_values], dim=0),
                image_grid_thw=torch.cat([nofocal_full_grid_thw, focal_full_grid_thw], dim=0),
                labels=labels,
                has_hard_negative=False,
                focal_pixel_values=focal_crop_pixel_values,
                focal_image_grid_thw=focal_crop_grid_thw,
                focal_image_ids=focal_image_ids,
                real_image_grid_thw=image_grid_thw,
            )
        else:
            # No focal images
            return dict(
                input_ids=input_ids,
                attention_mask=attention_mask,
                pixel_values=pixel_values,
                image_grid_thw=image_grid_thw,
                labels=labels,
                has_hard_negative=False,
                focal_pixel_values=None,
                focal_image_grid_thw=None,
                focal_image_ids=None,
                real_image_grid_thw=image_grid_thw,
            )


if __name__ == "__main__":
    # Example usage
    datapath = "/mnt/tidal-alsh01/dataset/mmeb/fg-clip"

    ds = FGCLIPDataset(datapath, max_length=1000)
    print(f"Dataset length: {len(ds)}")
    print(f"Number of parquet files: {len(ds.parquet_files)}")

    # Test getting an item
    try:
        item1, item2 = ds[0]
        print("Successfully loaded first item")
        print(f"Item 1 structure: {type(item1)}")
        print(f"User content keys: {item1[0]['content'][0].keys()}")
    except Exception as e:
        print(f"Error loading item: {e}")
