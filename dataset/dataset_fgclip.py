
import os
import copy
from dataclasses import dataclass, field
import json
import logging
import pathlib
from typing import Dict, Optional, Sequence, List

import torch
import random

import glob
import transformers

from torch.utils.data import Dataset


class FGCLIPBboxDataset(Dataset):
    """Dataset for Stage2."""

    def __init__(self, data_path: str,
                 data_args,
                 img_preprocess=None,tokenizer=None):
        super(FGCLIPBboxDataset, self).__init__()

        if data_path.endswith('.json') or data_path.endswith('.jsonl'):
            list_data_dict = json.load(open(data_path, "r", encoding="utf-8"))
        elif data_path.endswith('.txt'):
            lines = open(data_path, "r", encoding="utf-8").readlines()
            list_data_dict = []
            for line in lines:
                json_file = line.rstrip()
                list_data_dict += json.load(open(json_file, "r",encoding="utf-8"))
        else:
            json_files = glob.glob(os.path.join(data_path, '*.json'))
            list_data_dict = []
            for json_file in json_files:
                list_data_dict += json.load(open(json_file, "r",encoding="utf-8"))

            jsonl_files = glob.glob(os.path.join(data_path, '*.jsonl'))
            for jsonl_file in jsonl_files:
                list_data_dict += json.load(open(jsonl_file, "r",encoding="utf-8"))
        

        self.tokenizer = tokenizer
        self.list_data_dict = list_data_dict
        self.max_anns = 4

        self.data_args = data_args
        self.preprocess = img_preprocess
        self.image_root = data_args.image_folder
        self.max_length = data_args.max_seq_length
        self.base_length = data_args.base_seq_length
        self.base_image_size = data_args.base_image_size
        self.add_box_loss = data_args.add_box_loss
        self.use_hard_neg = data_args.use_hard_neg

    def __len__(self):
        return len(self.list_data_dict)

    def __getitem__(self, i) -> Dict[str, torch.Tensor]:

        item = self.list_data_dict[i]

        caption = item["caption"]
        caption_short = "a photo of "+item["short_caption"]        

        image_path = item["f_path"]
        image_path = image_path.replace("grit-20m/data-12m/","")
        image_name = os.path.join(self.image_root,image_path)
        
        image = Image.open(image_name).convert("RGB")
        
        image = image.resize((self.base_image_size, self.base_image_size))

        image_tensor = self.preprocess.preprocess(image, return_tensors='pt')['pixel_values'][0]

        text =  torch.tensor(self.tokenizer([caption], max_length=self.max_length, padding="max_length", truncation=True).input_ids, dtype=torch.long, device=image_tensor.device)
        short_text = torch.tensor(self.tokenizer([caption_short], max_length=self.base_length, padding="max_length", truncation=True).input_ids, dtype=torch.long, device=image_tensor.device)        

        if self.add_box_loss:
            box_texts = []
            bbox_info = item["bbox_info"]

            total_num = self.max_anns
            valid_num = min(len(bbox_info), self.max_anns)
            boxes_template = torch.zeros((total_num, 4), device=image_tensor.device)
            width, height = image.size
            for i in range(total_num):
                if i<valid_num:
                    bbox_data = bbox_info[i]
                    box = bbox_data["bbox"]
                    box_caption = random.choice([bbox_data["short_expr"], bbox_data["long_expr"]])

                else:
                    box = [0.0000000, 0.0000000, 0.0000000, 0.0000000, 0.000000000]
                    box_caption = ""
                    

                box_tensor = torch.tensor(box[:4])
                boxes_template[i] = box_tensor

                if box[0] > box[2] or box[1] > box[3]:
                    raise ValueError("Box coordinates are invalid.")

                box_text = torch.tensor(self.tokenizer([box_caption], max_length=self.base_length, padding="max_length", truncation=True).input_ids, dtype=torch.long, device=image_tensor.device)        
                box_texts.append(box_text)


            box_texts = torch.cat(box_texts,dim=0)
            bbox_num = torch.tensor([valid_num], device=image_tensor.device)
                    
        if self.use_hard_neg:
            hard_texts = []
           
            bbox_info = item["bbox_info"]

            width, height = image.size
            total_num = self.max_anns
            valid_num = min(len(bbox_info), self.max_anns)
            hard_boxes = torch.zeros((total_num, 4), device=image_tensor.device)
            valid_hard = 0
            for i in range(total_num):
                if i<valid_num:
                    bbox_data = bbox_info[i]
                    box = bbox_data["bbox"]
                    box_caption = bbox_data["short_expr"]
                    
                    box_tensor = torch.tensor(box[:4])

                    if box[0] > box[2] or box[1] > box[3]:
                        raise ValueError("Box coordinates are invalid.")
    
                    if bbox_data["flag_short_neg"] == 1:
                        cur_texts = [box_caption]
                        hard_negs = bbox_data["short_expr_negs"]
                        for key in hard_negs.keys():
                            cur_texts.append(hard_negs[key])
                        box_text = torch.tensor(self.tokenizer(cur_texts, max_length=self.base_length, padding="max_length", truncation=True).input_ids, dtype=torch.long, device=image_tensor.device)        
                        hard_texts.append(box_text)

                        hard_boxes[valid_hard] = box_tensor
                        valid_hard = valid_hard+1


            valid_hard = torch.tensor([valid_hard], device=image_tensor.device)

            if len(hard_texts) > 0:
                hard_texts = torch.cat(hard_texts,dim=0)
            else:
                hard_texts = None

        data_dict = {}
        data_dict['image'] = image_tensor
        data_dict['text'] = text
        data_dict['short_text'] = short_text
        data_dict['add_box_loss'] = self.add_box_loss
        data_dict['use_hard_neg'] = self.use_hard_neg

        if self.add_box_loss:
            data_dict['box_texts'] = box_texts
            data_dict['box_infos'] = boxes_template
            data_dict['box_nums'] = bbox_num
        if self.use_hard_neg:
            data_dict['hard_texts'] = hard_texts
            data_dict['hard_infos'] = hard_boxes
            data_dict['hard_nums'] = valid_hard
            
        return data_dict


@dataclass
class FGCLIPDataCollator(object):
    """Collate examples for supervised fine-tuning."""

    def __call__(self, instances: Sequence[Dict]) -> Dict[str, torch.Tensor]:
        
        batch = {}
        images = [instance['image'] for instance in instances]
        batch['image'] = torch.stack(images)
        texts = [instance['text'] for instance in instances]
        batch['text_long'] = torch.cat(texts,dim=0)
        short_texts = [instance['short_text'] for instance in instances]
        batch['text_short'] = torch.cat(short_texts,dim=0)
        
        batch["add_box_loss"] = instances[0]["add_box_loss"]
        batch["use_hard_neg"] = instances[0]["use_hard_neg"]
        
        if batch["add_box_loss"]:
            box_texts = [instance['box_texts'] for instance in instances]
            batch['box_texts'] = torch.cat(box_texts,dim=0)
            box_infos = [instance['box_infos'] for instance in instances]
            batch['box_infos'] = torch.cat(box_infos,dim=0)
            box_nums = [instance['box_nums'] for instance in instances]
            batch['box_nums'] = torch.cat(box_nums, dim=0)
        if batch["use_hard_neg"] :
            hard_texts = []
            for instance in instances:
                if instance['hard_texts'] != None:
                    hard_texts.append(instance['hard_texts'])

            batch['hard_texts'] = torch.cat(hard_texts,dim=0)
            hard_infos = [instance['hard_infos'] for instance in instances]
            batch['hard_infos'] = torch.cat(hard_infos,dim=0)
            hard_nums = [instance['hard_nums'] for instance in instances]
            batch['hard_nums'] = torch.cat(hard_nums, dim=0)                

        return batch
    
#### utils #####
ROOT_DIR = "/mnt/tidal-alsh01/dataset/mmeb/fg-clip"
NAME_LIST = ['coyo_image_0','coyo_image_1']
NAME_PATTERN = re.compile(r"(coyo_image_\d+/\d{5})")
box_json_dir = os.path.join(ROOT_DIR, "./json_files/coyo01")

import re
from tqdm import tqdm

def extract_coyo_jsondicts(box_json_dir):

    def extract_coyo_images_from_json_dir(json_dir):
        result_dict = {}
        for filename in tqdm(os.listdir(json_dir)):
            if filename.endswith(".json"):
                filepath = os.path.join(json_dir, filename)
                with open(filepath, 'r', encoding='utf-8') as f:
                    try:
                        data_list = json.load(f)
                        for item in data_list:
                            if isinstance(item, dict):
                                f_path = item.get('f_path', '')
                                match = NAME_PATTERN.search(f_path)
                                if match:
                                    key = match.group(1)
                                    if key not in result_dict:
                                        result_dict[key] = []
                                    result_dict[key].append(item)
                    except Exception as e:
                        print(f"Failed to parse {filename}: {e}")
        return result_dict

    json_dir = os.path.join(ROOT_DIR, "json_files")
    filtered_dict = extract_coyo_images_from_json_dir(json_dir)

    # 为每个 key 单独写一个 JSON 文件
    os.makedirs(box_json_dir, exist_ok=True)
    for key, item_list in filtered_dict.items():
        safe_key = key.replace("/", "_")  # 文件名中不能有 /
        out_path = os.path.join(box_json_dir, f"{safe_key}.json")
        with open(out_path, 'w', encoding='utf-8') as f:
            json.dump(item_list, f, ensure_ascii=False, indent=2)

extract_coyo_jsondicts(box_json_dir)

def load_url2key_dict():
    json_dir = os.path.join(ROOT_DIR, "url2key_jsons/")
    url2key = {}
    for name in NAME_LIST:
        fname = os.path.join(json_dir, f"url2key_{name}.json")
        with open(fname, 'r', encoding='utf-8') as f:
            data = json.load(f)
            url2key[name] = data
    return url2key

url2key = load_url2key_dict()

def find_parquet_files():
    parquet_files = []
    for name in NAME_LIST:
        root_dir = os.path.join(ROOT_DIR, "down-grit-12m", name)
        for dirpath, dirnames, filenames in os.walk(root_dir):
            for filename in filenames:
                if filename.endswith('.parquet'):
                    parquet_files.append(os.path.join(dirpath, filename))
    return parquet_files

parquet_path_list = find_parquet_files()

image_count = 0  # 用于编号图片名
buffer = []

import pandas as pd

# with open('filtered_coyo_images.json', 'r', encoding='utf-8') as f:
#     json_dict = json.load(f)
#     json_item_dict = {item['f_path'][18:-4]: item for item in json_dict}
#     # "grit-20m/data-12m/coyo_image_5/00088/000885921.jpg" => coyo_image_5/00088/000885921

for parquet_file in parquet_path_list:
    # key1: 判断属于哪个coyo目录
    base_dir = None
    for x in NAME_LIST:
        if x in parquet_file:
            base_dir = x
            break

    match = NAME_PATTERN.search(parquet_file)
    key = match.group(1)
    safe_key = key.replace("/", "_")
    
    # 打开对应的目录
    with open(os.path.join(box_json_dir, f"{safe_key}.json"), 'r', encoding='utf-8') as f:
        json_dict = json.load(f)
        json_item_dict = {item['f_path'][18:-4]: item for item in json_dict}

    df = pd.read_parquet(parquet_file)
    for i, row in df.iterrows():
        if row['image'] is not None:
            url = row['url']
            img_key = url2key[base_dir].get(url)
            # assert key is not None, f"URL {url} not found in url2key for {base_dir}"
            key2 = key + '/' + img_key  # 如coyo_image_1/00001/000528.jpg

            item = json_item_dict.get(key2)
            assert item is not None, f"Key {key2} not found in json_dict"
            item.update({"image": row['image']}) 

            buffer.append(item)
            image_count += 1
            # 满1万存一次
            if len(buffer) == 10000:
                pd.DataFrame(buffer).to_parquet(f'part_{image_count//10000}.parquet')
                buffer = []
                print("save!")
# 存最后无法整万的数据
if buffer:
    pd.DataFrame(buffer).to_parquet(f'part_{image_count//10000+1}.parquet')
